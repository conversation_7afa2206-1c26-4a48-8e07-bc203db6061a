using System;
using System.Diagnostics;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom.ArchitectureBridge
{
    /// <summary>
    /// Architecture bridge that handles communication between x64 main process and x86 APCI libraries
    /// This solves the Error 193 architecture mismatch issue by using process isolation
    /// </summary>
    public class VocomArchitectureBridge : IDisposable
    {
        private readonly ILoggingService _logger;
        private Process? _bridgeProcess;
        private NamedPipeServerStream? _pipeServer;
        private NamedPipeClientStream? _pipeClient;
        private readonly string _pipeName;
        private bool _isInitialized;
        private bool _disposed;

        /// <summary>
        /// Gets whether the bridge is available and ready for use
        /// </summary>
        public bool IsAvailable => _isInitialized && _bridgeProcess != null && !_bridgeProcess.HasExited;

        public VocomArchitectureBridge(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            // Use process ID and timestamp to ensure unique pipe name
            _pipeName = $"VocomBridge_{Environment.ProcessId}_{DateTime.Now.Ticks}";
        }

        /// <summary>
        /// Initializes the architecture bridge by starting the x86 bridge process
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom Architecture Bridge", "VocomArchitectureBridge");

                // Check if already initialized
                if (_isInitialized)
                {
                    _logger.LogInformation("Bridge already initialized", "VocomArchitectureBridge");
                    return true;
                }

                // Create named pipe server for communication
                _pipeServer = new NamedPipeServerStream(_pipeName, PipeDirection.InOut, 1, PipeTransmissionMode.Message);

                // Start the x86 bridge process
                if (!await StartBridgeProcessAsync())
                {
                    _logger.LogError("Failed to start bridge process", "VocomArchitectureBridge");
                    return false;
                }

                // Wait for bridge process to connect
                _logger.LogInformation("Waiting for bridge process to connect...", "VocomArchitectureBridge");
                await _pipeServer.WaitForConnectionAsync();
                _logger.LogInformation("Bridge process connected successfully", "VocomArchitectureBridge");

                // Send initialization command
                var initCommand = new BridgeCommand
                {
                    Type = "Initialize",
                    Data = JsonSerializer.Serialize(new { LibrariesPath = GetLibrariesPath() })
                };

                var response = await SendCommandAsync(initCommand);
                if (response?.Success == true)
                {
                    _isInitialized = true;
                    _logger.LogInformation("Architecture bridge initialized successfully", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Bridge initialization failed: {response?.Message}", "VocomArchitectureBridge");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during bridge initialization: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Detects Vocom devices through the architecture bridge
        /// </summary>
        public async Task<VocomDevice[]> DetectDevicesAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot detect devices", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }

            try
            {
                var command = new BridgeCommand { Type = "DetectDevices" };
                var response = await SendCommandAsync(command);

                if (response?.Success == true && !string.IsNullOrEmpty(response.Data))
                {
                    var devices = JsonSerializer.Deserialize<VocomDevice[]>(response.Data);
                    _logger.LogInformation($"Detected {devices?.Length ?? 0} Vocom devices through bridge", "VocomArchitectureBridge");
                    return devices ?? Array.Empty<VocomDevice>();
                }

                _logger.LogWarning($"Device detection failed: {response?.Message}", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device detection: {ex.Message}", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device through the architecture bridge
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(string deviceId)
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot connect to device", "VocomArchitectureBridge");
                return false;
            }

            try
            {
                var command = new BridgeCommand
                {
                    Type = "ConnectDevice",
                    Data = JsonSerializer.Serialize(new { DeviceId = deviceId })
                };

                var response = await SendCommandAsync(command);
                if (response?.Success == true)
                {
                    _logger.LogInformation($"Successfully connected to device {deviceId} through bridge", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Failed to connect to device {deviceId}: {response?.Message}", "VocomArchitectureBridge");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device connection: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Sends a command to the bridge process and waits for response
        /// </summary>
        private async Task<BridgeResponse?> SendCommandAsync(BridgeCommand command)
        {
            if (_pipeServer == null || !_pipeServer.IsConnected)
            {
                _logger.LogError("Pipe server not connected", "VocomArchitectureBridge");
                return null;
            }

            try
            {
                // Serialize and send command
                var commandJson = JsonSerializer.Serialize(command);
                var commandBytes = Encoding.UTF8.GetBytes(commandJson);

                await _pipeServer.WriteAsync(commandBytes, 0, commandBytes.Length);
                await _pipeServer.FlushAsync();

                // Read response
                var buffer = new byte[4096];
                var bytesRead = await _pipeServer.ReadAsync(buffer, 0, buffer.Length);
                var responseJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);

                return JsonSerializer.Deserialize<BridgeResponse>(responseJson);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during command communication: {ex.Message}", "VocomArchitectureBridge");
                return null;
            }
        }

        /// <summary>
        /// Starts the x86 bridge process
        /// </summary>
        private async Task<bool> StartBridgeProcessAsync()
        {
            try
            {
                var bridgeExePath = GetBridgeExecutablePath();
                if (!File.Exists(bridgeExePath))
                {
                    _logger.LogError($"Bridge executable not found at {bridgeExePath}", "VocomArchitectureBridge");
                    return false;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = bridgeExePath,
                    Arguments = _pipeName,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                _bridgeProcess = Process.Start(startInfo);
                if (_bridgeProcess == null)
                {
                    _logger.LogError("Failed to start bridge process", "VocomArchitectureBridge");
                    return false;
                }

                _logger.LogInformation($"Started bridge process with PID {_bridgeProcess.Id}", "VocomArchitectureBridge");

                // Give the process a moment to start
                await Task.Delay(1000);

                return !_bridgeProcess.HasExited;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception starting bridge process: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Gets the path to the bridge executable
        /// </summary>
        private string GetBridgeExecutablePath()
        {
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");
        }

        /// <summary>
        /// Gets the libraries path for the bridge process
        /// </summary>
        private string GetLibrariesPath()
        {
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appPath, "Libraries");
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _pipeServer?.Dispose();
                _pipeClient?.Dispose();

                if (_bridgeProcess != null && !_bridgeProcess.HasExited)
                {
                    _bridgeProcess.Kill();
                    _bridgeProcess.Dispose();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disposal: {ex.Message}", "VocomArchitectureBridge");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Command sent to the bridge process
    /// </summary>
    public class BridgeCommand
    {
        public string Type { get; set; } = string.Empty;
        public string? Data { get; set; }
    }

    /// <summary>
    /// Response from the bridge process
    /// </summary>
    public class BridgeResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? Data { get; set; }
    }
}
